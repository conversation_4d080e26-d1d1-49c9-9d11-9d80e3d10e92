import axios from '@/services/axios';

const passwordResetService = {
  /**
   * Enviar link de reset de senha
   * @param {string} email 
   * @returns {Promise}
   */
  async sendResetLink(email) {
    try {
      const response = await axios.post('/password/send-reset-link', {
        email: email
      });
      
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Erro ao enviar link de reset:', error);
      
      return {
        success: false,
        error: error.response?.data?.message || 'Erro ao enviar link de redefinição'
      };
    }
  },

  /**
   * Validar token de reset
   * @param {string} token 
   * @param {string} email 
   * @returns {Promise}
   */
  async validateResetToken(token, email) {
    try {
      const response = await axios.post('/password/validate-token', {
        token: token,
        email: email
      });
      
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Erro ao validar token:', error);
      
      return {
        success: false,
        error: error.response?.data?.message || 'Token inválido ou expirado'
      };
    }
  },

  /**
   * Redefinir senha
   * @param {string} token 
   * @param {string} email 
   * @param {string} password 
   * @param {string} passwordConfirmation 
   * @returns {Promise}
   */
  async resetPassword(token, email, password, passwordConfirmation) {
    try {
      const response = await axios.post('/password/reset', {
        token: token,
        email: email,
        password: password,
        password_confirmation: passwordConfirmation
      });
      
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Erro ao redefinir senha:', error);
      
      return {
        success: false,
        error: error.response?.data?.message || 'Erro ao redefinir senha',
        errors: error.response?.data?.errors || {}
      };
    }
  }
};

export default passwordResetService;
